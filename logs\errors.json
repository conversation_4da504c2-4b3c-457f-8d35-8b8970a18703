[{"timestamp": "2025-06-19T00:20:10.602957", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments", "context": {}, "exception_type": "HTTPError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 80, in setup_instruments\n    self.instrument_manager.download_instruments(force_refresh)\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 70, in download_instruments\n    response.raise_for_status()\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\requests\\models.py\", line 1021, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments\n"}, {"timestamp": "2025-06-19T00:22:09.824464", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments", "context": {}, "exception_type": "HTTPError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 80, in setup_instruments\n    self.instrument_manager.download_instruments(force_refresh)\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 70, in download_instruments\n    response.raise_for_status()\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\requests\\models.py\", line 1021, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments\n"}, {"timestamp": "2025-06-19T00:26:32.099644", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments", "context": {}, "exception_type": "HTTPError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 80, in setup_instruments\n    self.instrument_manager.download_instruments(force_refresh)\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 70, in download_instruments\n    response.raise_for_status()\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\requests\\models.py\", line 1021, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments\n"}]