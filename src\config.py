"""
Configuration module for DhanHQ Option Data Fetcher.

This module handles API configuration, authentication, and application constants.
"""

import os
import logging
from typing import Dict, Optional
from dotenv import load_dotenv


class DhanConfig:
    """Configuration class for DhanHQ API and application settings."""
    
    # API Endpoints
    BASE_URL = "https://api.dhan.co"
    INSTRUMENTS_URL = f"{BASE_URL}/v2/instruments"
    HISTORICAL_DATA_URL = f"{BASE_URL}/v2/charts/intraday"
    WEBSOCKET_URL = "wss://api.dhan.co/v2/websocket"
    
    # WebSocket Configuration
    HEARTBEAT_INTERVAL = 30  # seconds
    RECONNECT_DELAY_MIN = 2  # seconds
    RECONNECT_DELAY_MAX = 60  # seconds
    MAX_RECONNECT_ATTEMPTS = 10
    
    # Data Configuration
    SUPPORTED_TIMEFRAMES = [1, 5, 60]  # minutes
    UPDATE_INTERVAL = 50  # seconds
    
    # File Paths
    DEFAULT_DATA_DIR = "data"
    DEFAULT_HISTORICAL_DIR = "data/historical"
    DEFAULT_REALTIME_DIR = "data/realtime"
    DEFAULT_LOG_DIR = "logs"
    
    # Rate Limiting
    API_RATE_LIMIT = 100  # requests per minute
    RATE_LIMIT_WINDOW = 60  # seconds
    
    def __init__(self, env_file: str = ".env"):
        """
        Initialize configuration by loading environment variables.
        
        Args:
            env_file (str): Path to the environment file
        """
        load_dotenv(env_file)
        self._validate_credentials()
        self._setup_directories()
        
    def _validate_credentials(self) -> None:
        """Validate that required credentials are present."""
        self.client_id = os.getenv("DHAN_CLIENT_ID")
        self.access_token = os.getenv("DHAN_ACCESS_TOKEN")
        
        if not self.client_id or not self.access_token:
            raise ValueError(
                "Missing required credentials. Please set DHAN_CLIENT_ID and "
                "DHAN_ACCESS_TOKEN in your .env file."
            )
    
    def _setup_directories(self) -> None:
        """Create necessary directories if they don't exist."""
        self.data_dir = os.getenv("DATA_DIR", self.DEFAULT_DATA_DIR)
        self.historical_dir = os.getenv("HISTORICAL_DATA_DIR", self.DEFAULT_HISTORICAL_DIR)
        self.realtime_dir = os.getenv("REALTIME_DATA_DIR", self.DEFAULT_REALTIME_DIR)
        self.log_dir = os.getenv("LOG_DIR", self.DEFAULT_LOG_DIR)
        
        for directory in [self.data_dir, self.historical_dir, self.realtime_dir, self.log_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers for API requests.
        
        Returns:
            Dict[str, str]: Headers dictionary with authentication
        """
        return {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {self.access_token}",
            "X-Client-Id": self.client_id
        }
    
    def get_log_level(self) -> str:
        """Get logging level from environment or default to INFO."""
        return os.getenv("LOG_LEVEL", "INFO").upper()
    
    def get_log_file(self) -> str:
        """Get log file path."""
        return os.getenv("LOG_FILE", f"{self.log_dir}/dhan_option_monitor.log")


class LoggerSetup:
    """Setup logging configuration for the application."""
    
    @staticmethod
    def setup_logger(config: DhanConfig, name: str = "dhan_monitor") -> logging.Logger:
        """
        Setup logger with file and console handlers.
        
        Args:
            config (DhanConfig): Configuration instance
            name (str): Logger name
            
        Returns:
            logging.Logger: Configured logger instance
        """
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, config.get_log_level()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler(config.get_log_file())
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, config.get_log_level()))
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        return logger


# Global configuration instance
config = None

def get_config(env_file: str = ".env") -> DhanConfig:
    """
    Get or create global configuration instance.
    
    Args:
        env_file (str): Path to environment file
        
    Returns:
        DhanConfig: Configuration instance
    """
    global config
    if config is None:
        config = DhanConfig(env_file)
    return config
