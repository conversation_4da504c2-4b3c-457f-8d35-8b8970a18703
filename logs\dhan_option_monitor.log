2025-06-19 00:20:10,251 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:20:10,251 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:20:10,251 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ API
2025-06-19 00:20:10,602 - instrument_manager - ERROR - download_instruments:87 - Failed to download instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:20:10,603 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 80, in setup_instruments
    self.instrument_manager.download_instruments(force_refresh)
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 70, in download_instruments
    response.raise_for_status()
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:22:09,400 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:22:09,401 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:22:09,401 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ API
2025-06-19 00:22:09,823 - instrument_manager - ERROR - download_instruments:87 - Failed to download instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:22:09,826 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 80, in setup_instruments
    self.instrument_manager.download_instruments(force_refresh)
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 70, in download_instruments
    response.raise_for_status()
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:26:31,726 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:26:31,726 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:26:31,726 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ API
2025-06-19 00:26:32,099 - instrument_manager - ERROR - download_instruments:87 - Failed to download instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:26:32,100 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 80, in setup_instruments
    self.instrument_manager.download_instruments(force_refresh)
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 70, in download_instruments
    response.raise_for_status()
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
