"""
Instrument Data Management System for DhanHQ Option Data Fetcher.

This module handles downloading instrument lists, parsing user options,
and mapping option names to instrument tokens.
"""

import os
import csv
import json
import logging
import requests
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import re

from .config import <PERSON>hanConfig, LoggerSetup


class InstrumentManager:
    """Manages instrument data and option mapping for DhanHQ API."""
    
    def __init__(self, config: DhanConfig):
        """
        Initialize the instrument manager.
        
        Args:
            config (DhanConfig): Configuration instance
        """
        self.config = config
        self.logger = LoggerSetup.setup_logger(config, "instrument_manager")
        self.instruments_df = None
        self.user_options = []
        self.mapped_instruments = []
        
    def download_instruments(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        Download and cache instrument list from DhanHQ API.
        
        Args:
            force_refresh (bool): Force download even if cached file exists
            
        Returns:
            pd.DataFrame: Instruments dataframe
            
        Raises:
            requests.RequestException: If API request fails
            ValueError: If response data is invalid
        """
        instruments_file = os.path.join(self.config.data_dir, "instruments.csv")
        
        # Check if we need to download
        if not force_refresh and os.path.exists(instruments_file):
            # Check if file is less than 24 hours old
            file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(instruments_file))
            if file_age < timedelta(hours=24):
                self.logger.info("Loading instruments from cached file")
                self.instruments_df = pd.read_csv(instruments_file)
                return self.instruments_df
        
        self.logger.info("Downloading instruments from DhanHQ API")
        
        try:
            response = requests.get(
                self.config.INSTRUMENTS_URL,
                headers=self.config.get_auth_headers(),
                timeout=30
            )
            response.raise_for_status()
            
            instruments_data = response.json()
            
            if not isinstance(instruments_data, list):
                raise ValueError("Invalid instruments data format")
            
            # Convert to DataFrame
            self.instruments_df = pd.DataFrame(instruments_data)
            
            # Save to cache
            self.instruments_df.to_csv(instruments_file, index=False)
            self.logger.info(f"Downloaded {len(self.instruments_df)} instruments")
            
            return self.instruments_df
            
        except requests.RequestException as e:
            self.logger.error(f"Failed to download instruments: {e}")
            raise
        except (ValueError, KeyError) as e:
            self.logger.error(f"Invalid instruments data: {e}")
            raise
    
    def load_user_options(self, csv_file: str = "User_options_input.csv") -> List[Dict]:
        """
        Load user options from CSV file.
        
        Args:
            csv_file (str): Path to user options CSV file
            
        Returns:
            List[Dict]: List of user options
            
        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV format is invalid
        """
        if not os.path.exists(csv_file):
            raise FileNotFoundError(f"User options file not found: {csv_file}")
        
        self.logger.info(f"Loading user options from {csv_file}")
        
        try:
            with open(csv_file, 'r', newline='', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                self.user_options = [row for row in reader if row.get('DISPLAY_NAME', '').strip()]
            
            self.logger.info(f"Loaded {len(self.user_options)} user options")
            return self.user_options
            
        except Exception as e:
            self.logger.error(f"Failed to load user options: {e}")
            raise ValueError(f"Invalid CSV format: {e}")
    
    def parse_option_name(self, display_name: str) -> Optional[Dict]:
        """
        Parse option display name to extract components.
        
        Args:
            display_name (str): Option display name (e.g., "NIFTY 26 JUN 24500 CALL")
            
        Returns:
            Optional[Dict]: Parsed option components or None if parsing fails
        """
        # Pattern for NIFTY options: "NIFTY DD MMM STRIKE CALL/PUT"
        pattern = r'^(\w+)\s+(\d{1,2})\s+(\w{3})\s+(\d+(?:\.\d+)?)\s+(CALL|PUT)$'
        
        match = re.match(pattern, display_name.strip().upper())
        if not match:
            self.logger.warning(f"Could not parse option name: {display_name}")
            return None
        
        symbol, day, month, strike, option_type = match.groups()
        
        # Convert month abbreviation to number
        month_map = {
            'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
            'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
        }
        
        month_num = month_map.get(month.upper())
        if not month_num:
            self.logger.warning(f"Invalid month in option name: {display_name}")
            return None
        
        # Determine year (assume current year or next year)
        current_year = datetime.now().year
        current_month = datetime.now().month
        
        # If the option month is before current month, assume next year
        year = current_year if month_num >= current_month else current_year + 1
        
        return {
            'symbol': symbol,
            'day': int(day),
            'month': month_num,
            'year': year,
            'strike': float(strike),
            'option_type': option_type,
            'display_name': display_name
        }
    
    def find_matching_instrument(self, parsed_option: Dict) -> Optional[Dict]:
        """
        Find matching instrument for parsed option.
        
        Args:
            parsed_option (Dict): Parsed option components
            
        Returns:
            Optional[Dict]: Matching instrument data or None
        """
        if self.instruments_df is None:
            self.logger.error("Instruments not loaded. Call download_instruments() first.")
            return None
        
        # Filter instruments by symbol and option type
        symbol_filter = self.instruments_df['SEM_SMST_SECURITY_ID'].str.contains(
            parsed_option['symbol'], case=False, na=False
        )
        
        # Filter by option type (CE for CALL, PE for PUT)
        option_type_code = 'CE' if parsed_option['option_type'] == 'CALL' else 'PE'
        type_filter = self.instruments_df['SEM_SMST_SECURITY_ID'].str.contains(
            option_type_code, case=False, na=False
        )
        
        # Filter by strike price
        strike_filter = self.instruments_df['SEM_STRIKE_PRICE'] == parsed_option['strike']
        
        # Combine filters
        filtered_df = self.instruments_df[symbol_filter & type_filter & strike_filter]
        
        if filtered_df.empty:
            self.logger.warning(f"No matching instrument found for {parsed_option['display_name']}")
            return None
        
        if len(filtered_df) > 1:
            # If multiple matches, try to find the closest expiry date
            target_date = datetime(parsed_option['year'], parsed_option['month'], parsed_option['day'])
            
            # Convert expiry dates and find closest
            filtered_df = filtered_df.copy()
            filtered_df['expiry_date'] = pd.to_datetime(filtered_df['SEM_EXPIRY_DATE'], errors='coerce')
            filtered_df['date_diff'] = abs((filtered_df['expiry_date'] - target_date).dt.days)
            
            # Get the row with minimum date difference
            best_match = filtered_df.loc[filtered_df['date_diff'].idxmin()]
        else:
            best_match = filtered_df.iloc[0]
        
        self.logger.info(f"Found matching instrument for {parsed_option['display_name']}: {best_match['SEM_SMST_SECURITY_ID']}")
        
        return best_match.to_dict()
    
    def map_user_options(self) -> List[Dict]:
        """
        Map user options to instrument data.
        
        Returns:
            List[Dict]: List of mapped instruments with metadata
        """
        if not self.user_options:
            raise ValueError("No user options loaded. Call load_user_options() first.")
        
        if self.instruments_df is None:
            raise ValueError("Instruments not loaded. Call download_instruments() first.")
        
        self.logger.info("Mapping user options to instruments")
        self.mapped_instruments = []
        
        for option in self.user_options:
            display_name = option.get('DISPLAY_NAME', '').strip()
            underlying_symbol = option.get('UNDERLYING_SYMBOL', '').strip()
            
            if not display_name:
                continue
            
            # Parse option name
            parsed_option = self.parse_option_name(display_name)
            if not parsed_option:
                continue
            
            # Find matching instrument
            instrument = self.find_matching_instrument(parsed_option)
            if not instrument:
                continue
            
            # Create mapped instrument entry
            mapped_instrument = {
                'display_name': display_name,
                'underlying_symbol': underlying_symbol,
                'instrument_token': instrument.get('SEM_INSTRUMENT_NAME'),
                'security_id': instrument.get('SEM_SMST_SECURITY_ID'),
                'exchange_segment': instrument.get('SEM_EXM_EXCH_ID'),
                'strike_price': instrument.get('SEM_STRIKE_PRICE'),
                'expiry_date': instrument.get('SEM_EXPIRY_DATE'),
                'option_type': parsed_option['option_type'],
                'lot_size': instrument.get('SEM_LOT_SIZE', 1),
                'tick_size': instrument.get('SEM_TICK_SIZE', 0.05),
                'parsed_option': parsed_option,
                'full_instrument_data': instrument
            }
            
            self.mapped_instruments.append(mapped_instrument)
        
        self.logger.info(f"Successfully mapped {len(self.mapped_instruments)} instruments")
        
        # Save mapped instruments to file
        self._save_mapped_instruments()
        
        return self.mapped_instruments
    
    def _save_mapped_instruments(self) -> None:
        """Save mapped instruments to JSON file."""
        output_file = os.path.join(self.config.data_dir, "mapped_instruments.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.mapped_instruments, f, indent=2, default=str)
            
            self.logger.info(f"Saved mapped instruments to {output_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save mapped instruments: {e}")
    
    def get_instrument_tokens(self) -> List[str]:
        """
        Get list of instrument tokens for WebSocket subscription.
        
        Returns:
            List[str]: List of instrument tokens
        """
        if not self.mapped_instruments:
            return []
        
        return [inst['instrument_token'] for inst in self.mapped_instruments if inst.get('instrument_token')]
    
    def get_mapped_instruments(self) -> List[Dict]:
        """
        Get the list of mapped instruments.
        
        Returns:
            List[Dict]: List of mapped instruments
        """
        return self.mapped_instruments
